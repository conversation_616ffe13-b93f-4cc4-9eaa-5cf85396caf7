/**
 * Simple, AI-friendly internationalization system
 * Designed for <PERSON> with easy AI translation workflow
 */

export type SupportedLocale = 'en' | 'pt' | 'es';

export interface TranslationMessages {
  [key: string]: string | TranslationMessages;
}

// Global translations cache
const translationsCache = new Map<SupportedLocale, TranslationMessages>();

/**
 * Load translations for a specific locale
 */
export async function loadTranslations(locale: SupportedLocale): Promise<TranslationMessages> {
  if (translationsCache.has(locale)) {
    return translationsCache.get(locale)!;
  }

  try {
    const translations = await import(`./messages/${locale}.json`);
    const messages = translations.default || translations;
    translationsCache.set(locale, messages);
    return messages;
  } catch (error) {
    console.warn(`Failed to load translations for locale: ${locale}`, error);
    // Fallback to English if available
    if (locale !== 'en') {
      return loadTranslations('en');
    }
    return {};
  }
}

/**
 * Get nested translation value using dot notation
 */
function getNestedValue(obj: any, path: string): string | undefined {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * Simple translation function
 * @param key - Translation key (supports dot notation: 'common.buttons.save')
 * @param locale - Target locale
 * @param fallback - Fallback text if translation not found
 * @param variables - Variables to interpolate in the translation
 */
export async function t(
  key: string,
  locale: SupportedLocale = 'en',
  fallback?: string,
  variables?: Record<string, string | number>
): Promise<string> {
  const messages = await loadTranslations(locale);
  let translation = getNestedValue(messages, key) || fallback || key;

  // Simple variable interpolation
  if (variables && typeof translation === 'string') {
    Object.entries(variables).forEach(([varKey, value]) => {
      translation = translation.replace(new RegExp(`\\{${varKey}\\}`, 'g'), String(value));
    });
  }

  return translation;
}

/**
 * React hook for translations
 */
export function useTranslations(locale: SupportedLocale = 'en') {
  return {
    t: (key: string, fallback?: string, variables?: Record<string, string | number>) => 
      t(key, locale, fallback, variables),
    locale
  };
}

/**
 * Map your existing language codes to our i18n system
 */
export function mapLanguageCode(langCode: string): SupportedLocale {
  const mapping: Record<string, SupportedLocale> = {
    'EN_US': 'en',
    'PT_BR': 'pt', 
    'ES_ES': 'es',
    'en': 'en',
    'pt': 'pt',
    'es': 'es'
  };
  
  return mapping[langCode] || 'en';
} 