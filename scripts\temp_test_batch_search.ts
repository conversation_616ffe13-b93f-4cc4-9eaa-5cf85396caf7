import { getBatchEmbeddingsService } from '../src/lib/ai/services/unified-embeddings.service';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env['NEXT_PUBLIC_SUPABASE_URL'];
const supabaseKey = process.env['NEXT_PUBLIC_SUPABASE_ANON_KEY'];

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Supabase URL and key must be provided from .env.local');
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testBatchSearch() {
  const words = ['lavender', 'orange', 'grapefruit', 'petitgrain', 'wintergreen'];
  console.log('🔍 Searching for words:', words);

  const embeddingService = getBatchEmbeddingsService();
  const embeddingsResponse = await embeddingService.createBatchEmbeddings({ texts: words });
  
  console.log('📊 Number of embeddings created:', embeddingsResponse.length);
  console.log('📊 Embedding dimensions:', embeddingsResponse[0]?.embedding.length);

  // First, test the single vector function to confirm our embedding format works
  console.log('\n🧪 Testing single vector function first...');
  const firstEmbedding = embeddingsResponse[0]?.embedding;
  if (!firstEmbedding) {
    console.error('❌ No embedding found in first response');
    return;
  }
  
  const { data: singleData, error: singleError } = await supabase.rpc('match_oils_with_safety', {
    query_embedding: firstEmbedding,
    match_threshold: 0.5,
    match_count: 3,
  });

  if (singleError) {
    console.error('❌ Single vector function failed:', singleError);
    return;
  }
  
  console.log('✅ Single vector function works! Found', singleData?.length || 0, 'results');

  // Now test the batch function
  console.log('\n🧪 Testing batch vector function...');
  
  // Convert embeddings to JSON strings - this is what vector[] type expects
  const embeddings = embeddingsResponse.map(e => JSON.stringify(e.embedding));
  
  console.log('📤 Sending to batch_similarity_search:');
  console.log('  - Number of embeddings:', embeddings.length);
  console.log('  - Vector format: JSON strings (required for vector[] type)');
  console.log('  - Match threshold:', 0.5);
  console.log('  - Match count per embedding:', 5);

  const { data, error } = await supabase.rpc('batch_similarity_search', {
    query_embeddings: embeddings,
    match_threshold: 0.5,
    match_count: 5,
  });

  if (error) {
    console.error('❌ Error calling batch_similarity_search:', error);
    return;
  }

  console.log('✅ Batch search results:');
  console.log('📊 Total results returned:', data?.length || 0);
  
  // Group results by embedding_index to show results per word
  const resultsByIndex = data?.reduce((acc: any, result: any) => {
    const index = result.embedding_index;
    if (!acc[index]) acc[index] = [];
    acc[index].push(result);
    return acc;
  }, {});

  console.log('📊 Results per word:');
  Object.keys(resultsByIndex || {}).forEach(index => {
    const wordIndex = parseInt(index) - 1; // Convert to 0-based for word array
    const word = words[wordIndex];
    const results = resultsByIndex[index];
    console.log(`  Word "${word}" (index ${index}): ${results.length} results`);
    
    // Show top result for each word
    if (results.length > 0) {
      const topResult = results[0];
      console.log(`    Top match: ${topResult.name_english} (similarity: ${topResult.similarity.toFixed(3)})`);
    }
  });

  console.log('\n📋 Full results summary (first 3):');
  data?.slice(0, 3).forEach((result: any, i: number) => {
    console.log(`${i + 1}. ${result.name_english} (Word: ${words[result.embedding_index - 1]}, Similarity: ${result.similarity.toFixed(3)})`);
  });
}

testBatchSearch();
