/**
 * @fileoverview Reusable navigation buttons component for Recipe Wizard steps.
 * Provides consistent styling, behavior, and accessibility across all flow pages.
 */

'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface RecipeNavigationButtonsProps {
  // Navigation handlers
  onPrevious?: () => void | Promise<void>;
  onNext?: () => void | Promise<void>;
  
  // Navigation state
  canGoPrevious?: boolean;
  canGoNext?: boolean;
  
  // Form/step state
  isValid?: boolean;
  isLoading?: boolean;
  
  // Custom labels
  previousLabel?: string;
  nextLabel?: string;
  
  // Status message
  statusMessage?: string;
  statusType?: 'success' | 'warning' | 'info';
  
  // Custom styling
  className?: string;
  
  // Show/hide elements
  showPrevious?: boolean;
  showNext?: boolean;
  showStatus?: boolean;
}

/**
 * Consistent navigation buttons for recipe wizard steps
 */
export function RecipeNavigationButtons({
  onPrevious,
  onNext,
  canGoPrevious = true,
  canGoNext = true,
  isValid = true,
  isLoading = false,
  previousLabel = 'Previous',
  nextLabel = 'Continue',
  statusMessage,
  statusType = 'success',
  className,
  showPrevious = true,
  showNext = true,
  showStatus = true,
}: RecipeNavigationButtonsProps) {
  
  const getStatusColor = () => {
    switch (statusType) {
      case 'success':
        return 'text-green-600';
      case 'warning':
        return 'text-amber-600';
      case 'info':
        return 'text-blue-600';
      default:
        return 'text-green-600';
    }
  };

  const getStatusBadgeVariant = () => {
    switch (statusType) {
      case 'success':
        return 'outline' as const;
      case 'warning':
        return 'secondary' as const;
      case 'info':
        return 'outline' as const;
      default:
        return 'outline' as const;
    }
  };

  return (
    <div className={cn("flex justify-between items-center pt-6", className)}>
      {/* Previous Button */}
      {showPrevious ? (
        <Button
          type="button"
          variant="outline"
          onClick={onPrevious}
          disabled={!canGoPrevious || isLoading}
          className="min-w-[100px]"
        >
          ← {previousLabel}
        </Button>
      ) : (
        <div /> // Spacer to maintain justify-between layout
      )}

      {/* Status and Next Button */}
      <div className="flex items-center space-x-4">
        {/* Status Message */}
        {showStatus && statusMessage && isValid && !isLoading && (
          <div className="flex items-center">
            {statusType === 'success' ? (
              <Badge 
                variant={getStatusBadgeVariant()} 
                className="border-green-200 bg-green-50 text-green-700"
              >
                ✓ {statusMessage}
              </Badge>
            ) : (
              <span className={cn("text-sm font-medium", getStatusColor())}>
                {statusMessage}
              </span>
            )}
          </div>
        )}

        {/* Next Button */}
        {showNext && (
          <Button
            type="button"
            onClick={onNext}
            disabled={!isValid || !canGoNext || isLoading}
            className="min-w-[120px]"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                {nextLabel} →
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  );
}

/**
 * Navigation buttons specifically for form submission steps
 */
export function RecipeFormNavigationButtons(props: Omit<RecipeNavigationButtonsProps, 'onNext'> & {
  onSubmit?: () => void | Promise<void>;
}) {
  return (
    <RecipeNavigationButtons
      {...props}
      onNext={props.onSubmit}
    />
  );
}

/**
 * Navigation buttons with streaming-aware status
 */
export function RecipeStreamingNavigationButtons(props: RecipeNavigationButtonsProps & {
  isStreaming?: boolean;
  streamingMessage?: string;
}) {
  const { isStreaming, streamingMessage, ...navigationProps } = props;
  
  return (
    <RecipeNavigationButtons
      {...navigationProps}
      isLoading={isStreaming || navigationProps.isLoading}
      statusMessage={isStreaming ? (streamingMessage || 'Analyzing...') : navigationProps.statusMessage}
      statusType={isStreaming ? 'info' : navigationProps.statusType}
      showStatus={isStreaming || navigationProps.showStatus}
    />
  );
} 