# Oil Enrichment and Refinement Pipeline

**Objective:** This document outlines the implementation plan for a data refinement pipeline. The goal is to process AI-generated essential oil suggestions to ensure data quality, safety, and relevance before they are presented to the user. This is a prescriptive guide for a necessary refactoring.

## 1. Current Data Flow vs. Proposed Refinement

The current process is divided into two main phases:

### Phase 1: AI Suggestion (`suggested-oils`)

1.  **Input**: The frontend sends user data (health concerns, demographics, etc.) to the AI.
2.  **AI Processing**: The AI, acting as an aromatherapy expert, generates a list of relevant essential oils.
3.  **Output**: A list of oils with `name_english`, a suggested `name_botanical`, and a `relevancy_to_property_score`.

**Challenge**: The AI's suggestions can be imperfect, with potential inaccuracies in botanical names or suggestions for oils not in our database. Therefore, this initial list requires validation.

### Phase 2: System Validation & Enrichment (`oil-enrichment`)

1.  **Input**: The frontend sends the AI-suggested oil list to the `/api/ai/batch-enrichment` endpoint.
2.  **Backend Processing**: The `BatchEnrichmentService` validates each oil against the Supabase database.
    *   **Validation**: Checks if the oil exists in our database.
    *   **Similarity Scoring**: Calculates a `similarity_score` between the suggested oil and database entries.
    *   **Enrichment**: Augments the oil data with critical information like safety, constituents, etc.
3.  **Output**: An "enriched" list of oils, each with an `enrichment_status` ('enriched', 'discarded', 'not_found'), a `similarity_score`, and validated safety data.

**Current Limitation:** The `properties-display.tsx` component currently performs a basic update, marking oils as `isEnriched: true` without applying the necessary refinement logic. This needs to be replaced with the pipeline described below.

## 2. Implementation: Frontend Refinement Pipeline

The strategic point for this implementation is in the `src/features/create-recipe/components/properties-display.tsx` component, specifically within the `handleStreamingResults` function. This is the only location where we have access to the original AI suggestions, the enriched data from the backend, and user demographics simultaneously.

The following refinement process should be implemented as a chained array manipulation pipeline:

```typescript
// In: src/features/create-recipe/components/properties-display.tsx

import _ from 'lodash';

// ... after receiving enrichmentResult from the API
const { demographics } = useRecipeStore.getState();

const finalOils = _.uniqBy(
  enrichmentResult.enriched_oils
    .map(enrichedOil => {
      // 1. Quality Migration & Scoring
      if (enrichedOil.enrichment_status === 'discarded' || enrichedOil.enrichment_status === 'not_found') {
        if (enrichedOil.enrichment_status === 'discarded') {
            console.log(`🗑️ MIGRATING: Discarding low similarity oil: "${enrichedOil.name_english}"`);
        } else {
            console.log(`❓ MIGRATING: Discarding unfound oil: "${enrichedOil.name_english}"`);
        }
        return null;
      }

      const confidence_score = (enrichedOil.relevancy_to_property_score / 5) * enrichedOil.similarity_score;

      if (enrichedOil.botanical_mismatch) {
        console.log(`🔄 MIGRATING: Correcting botanical name for "${enrichedOil.name_english}" -> "${enrichedOil.name_scientific}"`);
      }

      return {
        ...enrichedOil,
        name_botanical: enrichedOil.botanical_mismatch ? enrichedOil.name_scientific : enrichedOil.name_botanical,
        isEnriched: true,
        confidence_score: parseFloat(confidence_score.toFixed(2)),
      };
    })
    .filter(Boolean) // Removes nulls (discarded oils)
    .filter(oil => { // 2. Demographic Filtering
      if (demographics.ageCategory === 'child' && (oil.safety?.dermocaustic || oil.safety?.neurotoxic)) {
          console.log(`🛡️ SAFETY FILTER: Removing "${oil.name_english}" for child.`);
          return false;
      }
      // Add other safety rules here (e.g., pregnancy, seniors)
      return true;
    }),
  'name_scientific' // 3. Deduplication
) as EnrichedEssentialOil[];

// Update the store with the final, clean, and safe list
updatePropertyWithEnrichedOils(p.property_id, finalOils);
setPropertyEnrichmentStatus(p.property_id, 'success');
console.log(`✅ Batch enrichment SUCCESS for ${p.property_name} with ${finalOils.length} valid oils.`);

```

### Refinement Strategies Summary

| Strategy                  | Objective                                       | Data Used                                           | Implementation Point |
| ------------------------- | ----------------------------------------------- | --------------------------------------------------- | -------------------- |
| **1. Quality & Scoring**  | Correct AI data & calculate `confidence_score`. | `enrichment_result`, `relevancy_score`              | `.map()`             |
| **2. Demographic Filter** | Ensure user safety.                             | `demographics.ageCategory`, `oil.safety`            | `.filter()`          |
| **3. Consolidation**      | Remove duplicates post-correction.              | validated `name_scientific`                         | `_.uniqBy()`         |
| **4. UI Demotion**        | Penalize uncertain matches.                     | `oil.botanical_mismatch`                            | UI Sorting Logic     |

## 3. UI Constraints and Integration

**Critical Constraint:** The visual presentation of the therapeutic properties and essential oils must remain consistent with the existing design. The component at `src/features/create-recipe/components/therapeutic-properties-table.tsx` should **not** be structurally refactored.

-   **No Layout Changes:** The existing table structure, expandable rows, and overall layout must be preserved.
-   **Adapt, Don't Replace:** The component should be adapted to display the newly refined data. For example:
    -   The `confidence_score` can be displayed as a new badge or integrated into the existing relevancy score.
    -   An `is_ultra_safe` flag could be used to show a specific icon or badge.
-   **`OilTableRow` Component:** The `OilTableRow` component within the table is the primary target for displaying the new information. Modifications should be contained within this component to the greatest extent possible.

## 4. Backend Enhancements

### Caching

To improve performance and reduce costs, a cache can be implemented in the `BatchEnrichmentService` to store results for frequently requested oils.

```typescript
// Example of an in-memory cache
const oilEnrichmentCache = new Map<string, EnrichedEssentialOil>();

function getCacheKey(nameEnglish: string, nameBotanical: string) {
  return `${nameEnglish.toLowerCase()}|${nameBotanical.toLowerCase()}`;
}

// Before processing an oil:
const cacheKey = getCacheKey(oil.name_english, oil.name_botanical);
if (oilEnrichmentCache.has(cacheKey)) {
  return oilEnrichmentCache.get(cacheKey);
}
// ... process and then store the result in the cache
oilEnrichmentCache.set(cacheKey, enrichedOil);
```

This unified approach ensures a single point of truth for data refinement, enhances the integrity of the application state, and provides a maintainable and performant solution.
