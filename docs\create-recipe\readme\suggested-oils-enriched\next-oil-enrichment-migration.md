# Estratégias de Refinamento para Sugestões de Óleos Essenciais

Este documento descreve o fluxo de dados para a sugestão e validação de óleos essenciais e propõe várias estratégias para refinar a lista inicial gerada pela IA, garan<PERSON><PERSON> maior precisão, segurança e relevância para o usuário final.

## 1. Visão Geral do Processo (Para um Desenvolvedor Júnior)

O processo de obter a lista final de óleos para cada propriedade terapêutica acontece em duas fases principais. Entender essa separação é fundamental para compreender por que o refinamento é tão importante.

### Fase 1: `suggested-oils` (A Sugestão Criativa da IA)

-   **O que acontece?** O frontend envia a preocupação de saúde, demografia, causas e sintomas do usuário para a nossa IA. A IA, agindo como uma especialista em aromaterapia, retorna uma lista de óleos essenciais que ela acredita serem relevantes para cada propriedade terapêutica.
-   **O que recebemos?** Uma lista de óleos com `name_english`, `name_botanical` (sugerido pela IA), e um `relevancy_to_property_score`.
-   **O "Problema":** A IA é criativa, mas não perfeita. Ela pode "inventar" nomes botânicos, sugerir óleos que não existem no nosso banco de dados ou não conhecer as informações de segurança específicas que temos. Não podemos confiar 100% nesta lista inicial.

### Fase 2: `oil-enrichment` (A Validação e Enriquecimento do Sistema)

-   **O que acontece?** O frontend pega a lista de óleos sugerida pela IA e a envia para o nosso próprio backend (`/api/ai/batch-enrichment`). Nosso sistema então compara cada óleo sugerido com o nosso banco de dados de óleos (Supabase).
-   **O que fazemos?**
    1.  **Validação:** Verificamos se o óleo realmente existe.
    2.  **Similaridade:** Calculamos o quão parecido o óleo sugerido é com os óleos do nosso banco (`similarity_score`).
    3.  **Enriquecimento:** Se encontramos uma correspondência, adicionamos dados cruciais que a IA não fornece, como `safety` (informações de segurança), `constituents`, etc.
-   **O Resultado:** Uma lista de óleos "enriquecida", onde cada óleo agora tem um `enrichment_status` ('enriched', 'discarded', 'not_found'), um `similarity_score`, e dados de segurança validados.

É neste ponto, após a Fase 2, que temos todos os dados necessários para aplicar as estratégias de refinamento inteligentes descritas abaixo, antes de salvar a lista final na store.

---

## 2. Estratégias de Refinamento e Filtragem

Estas são as lógicas que podemos aplicar no frontend (especificamente no `properties-display.tsx`) após receber a resposta do `oil-enrichment` e antes de atualizar a store do Zustand.

### Estratégia 1: Migração de Qualidade de Dados (A Base)

Esta é a lógica fundamental para corrigir os erros da IA.

-   **Objetivo:** Corrigir ou descartar óleos com base na validação do banco de dados.
-   **Regras:**
    -   **⚠️ Alta Similaridade (>0.65) mas Nome Botânico Diferente:** O óleo é mantido, mas seu nome botânico é **corrigido** para o que veio do banco de dados. Uma flag `botanical_mismatch: true` é mantida para indicar que houve uma correção.
    -   **❌ Baixa Similaridade:** O óleo é completamente **descartado** da lista para evitar fornecer informações de segurança incorretas.

### Estratégia 2: Scoring e Ranking Avançado

-   **Objetivo:** Criar scores mais inteligentes para ordenar os óleos, mostrando os mais promissores primeiro.

#### a) Score de Confiança (`confidence_score`)

-   **Lógica:** Combina a relevância sugerida pela IA com a certeza da nossa validação.
-   **Cálculo:** `confidence_score = (oil.relevancy_to_property_score / 5) * oil.similarity_score`
-   **Exemplo:** Um óleo com relevância 4/5 (0.8) e similaridade de 0.95 teria um score de confiança de `0.76`. Um óleo com relevância 5/5 (1.0) mas similaridade de 0.6 teria um score de `0.6`.
-   **Uso:** Reordenar a lista de óleos dentro de cada propriedade com base neste score.

#### b) Score de Relevância na Receita (`recipe_relevancy_score`)

-   **Lógica:** Pondera a relevância de um óleo para uma propriedade pelo "peso" da própria propriedade na receita geral.
-   **Cálculo:** `recipe_relevancy_score = oil.relevancy_to_property_score * property.relevancy_score`
-   **Uso:** Útil na etapa final de seleção da receita, para ajudar a escolher entre óleos de diferentes propriedades.

### Estratégia 3: Filtragem Baseada em Segurança e Demografia

-   **Objetivo:** Garantir a segurança do usuário, removendo proativamente óleos inadequados com base em seus dados demográficos.

#### a) Filtragem por Categoria de Idade

-   **Lógica:** Aplicar regras de exclusão com base na idade do usuário e nos dados de segurança do óleo.
-   **Exemplo de Regra:**
    -   **SE** `demographics.ageCategory` for `'child'` (Criança), **ENTÃO** remover todos os óleos onde:
        -   `oil.safety.dermocaustic === true`
        -   `oil.safety.neurotoxic === true`
        -   `oil.safety.photosensitive === true`
-   **Expansão:** Criar conjuntos de regras para outras categorias, como `'senior'` ou para usuárias grávidas (se essa informação for coletada).

#### b) Flag de Segurança Máxima (`is_ultra_safe`)

-   **Lógica:** Criar uma flag booleana para identificar os óleos mais seguros e fáceis de usar, especialmente para iniciantes.
-   **Cálculo da Flag:** `is_ultra_safe = oil.safety.dilution_level === 'low' && oil.safety.photosensitive === false && oil.safety.ingestion_safe === true`
-   **Uso:** Pode ser usado para destacar visualmente (com um selo "Fácil de Usar" 🛡️) ou para permitir que o usuário filtre por óleos "ultra seguros".

### Estratégia 4: Consolidação e Limpeza da Lista

-   **Objetivo:** Apresentar uma lista de óleos mais limpa e profissional.

#### a) Consolidação de Óleos Duplicados

-   **Lógica:** A IA pode sugerir "Lavanda" e "Lavanda Verdadeira". Nosso enriquecimento mapeará ambos para `Lavandula angustifolia`. Após a migração, devemos remover as duplicatas.
-   **Implementação:** Usar o `name_scientific` (que foi validado e corrigido) como chave única para desduplicar a lista de óleos.
-   **Benefício:** Evita que o usuário veja o mesmo óleo listado duas vezes sob nomes diferentes.

#### b) Rebaixamento de Óleos com `botanical_mismatch`

-   **Lógica:** Mesmo que um óleo com `botanical_mismatch: true` seja mantido, ele representa um "palpite" do nosso sistema. Ele deve ter uma prioridade menor.
-   **Implementação:** Ao ordenar a lista, colocar os óleos com `botanical_mismatch: true` no final, ou aplicar um redutor no seu `confidence_score`.

---

## 3. Resumo das Propostas

| Estratégia | Objetivo | Dados Utilizados | Momento da Aplicação |
| :--- | :--- | :--- | :--- |
| **Migração de Qualidade** | Corrigir e limpar dados da IA | `enrichment_status`, `similarity_score`, `name_scientific` | Pós-enriquecimento |
| **Score de Confiança** | Ordenar por relevância e certeza | `relevancy_to_property_score`, `similarity_score` | Pós-migração |
| **Score de Relevância** | Ponderar óleo pela importância da propriedade | `oil.relevancy_score`, `property.relevancy_score` | Pós-migração |
| **Filtro por Idade** | Garantir segurança demográfica | `demographics.ageCategory`, `oil.safety` | Pós-migração |
| **Flag `is_ultra_safe`** | Identificar óleos para iniciantes | `oil.safety` (diluição, fotossensibilidade, etc.) | Pós-migração |
| **Consolidação** | Remover duplicatas | `name_scientific` validado | Pós-migração |
| **Rebaixamento** | Penalizar correspondências incertas | `botanical_mismatch` | Durante a ordenação |

# Plano de Migração e Refinamento de Dados Pós-Enriquecimento

Este documento detalha a estratégia de implementação para refinar as sugestões de óleos essenciais da IA, aplicando uma lógica de "migração" de dados no frontend. O objetivo é garantir que apenas informações precisas e seguras sejam armazenadas no estado da aplicação (Zustand store) e apresentadas ao usuário.

## 1. Entendendo o Fluxo de Dados (Backend -> Frontend)

Para implementar a migração corretamente, é crucial entender o fluxo de dados que começa após a IA sugerir os óleos.

```mermaid
sequenceDiagram
    participant Comp as properties-display.tsx (Frontend)
    participant Store as Zustand Store (Frontend)
    participant API as /api/ai/batch-enrichment (Backend)
    participant Service as BatchEnrichmentService (Backend)
    participant DB as Supabase (Backend)

    Note over Comp: A IA já sugeriu os óleos.
    Comp->>+API: 1. fetch({ suggestedOils: [...] })
    API->>+Service: 2. Chama batchEnrichOils(oils)
    Service->>+DB: 3. Chama RPC batch_similarity_search()
    DB-->>-Service: 4. Retorna óleos correspondentes com `similarity_score` e `name_scientific`
    Service-->>-API: 5. Retorna `enriched_oils` com `enrichment_status` e `botanical_mismatch`
    API-->>-Comp: 6. Retorna resposta JSON (enrichmentResult)
    
    Note over Comp: Ponto de Intervenção Estratégico!
    Comp->>Comp: 7. **Aplica a Lógica de Migração**
    
    Comp->>+Store: 8. updatePropertyWithEnrichedOils(migratedOils)
    Store-->>-Comp: 9. Estado atualizado com dados limpos

Resumo do Fluxo:

Gatilho: O componente properties-display.tsx recebe a lista de suggested_oils da IA.
Requisição: Ele imediatamente faz uma chamada fetch para o endpoint /api/ai/batch-enrichment, enviando essa lista.
Processamento (Backend): O backend (especificamente o BatchEnrichmentService) valida cada óleo contra o banco de dados (Supabase), gerando o similarity_score e o enrichment_status ('enriched', 'discarded', etc.). Se a similaridade for alta mas o nome botânico for diferente, ele adiciona a flag botanical_mismatch: true.
Resposta: O backend retorna um objeto JSON (enrichmentResult) contendo a lista de enriched_oils.
Ponto de Intervenção: Aqui, dentro do properties-display.tsx, antes de salvar os dados, aplicamos nossa lógica de migração.
Armazenamento: O componente chama a ação updatePropertyWithEnrichedOils do Zustand para salvar a lista de óleos já migrada e limpa no estado global.
2. O Ponto de Intervenção Estratégico (Onde a Mágica Acontece)
A implementação deve ser feita em um único local para seguir o princípio DRY (Don't Repeat Yourself).

Arquivo: src/features/create-recipe/components/properties-display.tsx
Local Exato: Dentro do useEffect que lida com os resultados do streaming (handleStreamingResults), na async IIFE (Immediately Invoked Function Expression) que faz a chamada fetch.
Por que aqui? É o único ponto no sistema onde temos acesso simultâneo a: a) A lista original de óleos sugeridos pela IA. b) A lista enriquecida e validada pelo nosso backend.

Ao processar os dados aqui, garantimos que qualquer dado que entre no Zustand Store já esteja 100% validado e em conformidade com nossas regras de negócio, simplificando todos os outros componentes que consomem esses dados.

3. Implementação Detalhada da Migração
A lógica de migração deve ser inserida logo após receber a resposta da API e antes de atualizar a store.

// Em: src/features/create-recipe/components/properties-display.tsx
// Dentro da função async que faz o fetch para /api/ai/batch-enrichment

// ...
const enrichmentResult = await response.json();

// --- INÍCIO DA LÓGICA DE MIGRAÇÃO E REFINAMENTO ---

const migratedAndRefinedOils = enrichmentResult.enriched_oils
  .map(enrichedOil => {
    // Caso 1: Alta Similaridade, mas nome botânico diferente.
    // Ação: Mantém o óleo, mas corrige o nome botânico para o validado pelo DB.
    if (enrichedOil.enrichment_status === 'enriched' && enrichedOil.botanical_mismatch) {
      console.log(`🔄 MIGRATING: Corrigindo nome botânico para "${enrichedOil.name_english}" -> "${enrichedOil.name_scientific}"`);
      return {
        ...enrichedOil,
        name_botanical: enrichedOil.name_scientific, // Correção do nome botânico
        isEnriched: true,
      };
    }

    // Caso 2: Óleo enriquecido com sucesso, sem divergências.
    // Ação: Apenas mantém o óleo e garante a flag de enriquecimento.
    if (enrichedOil.enrichment_status === 'enriched') {
      return {
        ...enrichedOil,
        isEnriched: true,
      };
    }

    // Caso 3: Baixa Similaridade, marcado para descarte.
    // Ação: Retorna null para que seja filtrado no próximo passo.
    if (enrichedOil.enrichment_status === 'discarded') {
      console.log(`🗑️ MIGRATING: Descartando óleo de baixa similaridade: "${enrichedOil.name_english}"`);
      return null;
    }

    // Caso 4: Óleo não encontrado no DB.
    // Ação (opcional): Pode ser descartado ou mantido com uma flag para a UI. Vamos descartá-lo por segurança.
    if (enrichedOil.enrichment_status === 'not_found') {
        console.log(`❓ MIGRATING: Descartando óleo não encontrado: "${enrichedOil.name_english}"`);
        return null;
    }

    return null; // Garante que qualquer outro status não esperado seja descartado.
  })
  .filter(Boolean) as EnrichedEssentialOil[]; // .filter(Boolean) remove todos os itens nulos da lista.

// --- FIM DA LÓGICA DE MIGRAÇÃO E REFINAMENTO ---

// Agora, atualizamos a store com a lista limpa e corrigida.
updatePropertyWithEnrichedOils(p.property_id, migratedAndRefinedOils);
setPropertyEnrichmentStatus(p.property_id, 'success');
console.log(`✅ Batch enrichment SUCCESS for ${p.property_name} with ${migratedAndRefinedOils.length} valid oils.`);

4. Propostas Adicionais de Refinamento (O Próximo Nível)
Com a base da migração estabelecida, podemos facilmente adicionar outras camadas de inteligência no mesmo local.

a) Adicionar "Score de Confiança"
Podemos calcular e salvar um score que combina a sugestão da IA com a nossa validação.

Implementação: Adicione o cálculo dentro do .map():

// Dentro do .map(), para cada óleo que não for descartado:
const confidence_score = (enrichedOil.relevancy_to_property_score / 5) * enrichedOil.similarity_score;

return {
  ...enrichedOil,
  name_botanical: enrichedOil.name_scientific,
  isEnriched: true,
  confidence_score: confidence_score.toFixed(2), // Salva o score no objeto do óleo
};

Uso: A UI pode usar o confidence_score para ordenar os óleos, mostrando os mais confiáveis primeiro.

b) Filtragem por Demografia do Usuário
Podemos aplicar filtros de segurança baseados na idade do usuário.

Implementação: Após o .filter(Boolean), adicione um novo filtro.

// ... após .filter(Boolean)
const { demographics } = useRecipeStore.getState(); // Pega a demografia da store

const demographicallyFilteredOils = migratedAndRefinedOils.filter(oil => {
    if (demographics.ageCategory === 'child' && oil.safety?.dermocaustic) {
        console.log(`🛡️ SAFETY FILTER: Removendo "${oil.name_english}" (dermocáustico) para criança.`);
        return false; // Remove o óleo
    }
    // Adicionar outras regras de segurança aqui...
    return true; // Mantém o óleo
});

// Use `demographicallyFilteredOils` para atualizar a store.
updatePropertyWithEnrichedOils(p.property_id, demographicallyFilteredOils);

c) Consolidação de Duplicatas
Se a IA sugerir "Lavanda" e "Lavanda Verdadeira", nosso enriquecimento pode mapear ambos para Lavandula angustifolia. Podemos remover a duplicata.

Implementação: Use uma biblioteca como lodash após a filtragem demográfica.

import _ from 'lodash';

// ... após a filtragem demográfica
const uniqueOils = _.uniqBy(demographicallyFilteredOils, 'name_scientific');

// Use `uniqueOils` para atualizar a store.
updatePropertyWithEnrichedOils(p.property_id, uniqueOils);

5. Benefícios Desta Abordagem
Ponto Único de Verdade: A lógica de refinamento vive em um só lugar.
Integridade do Estado: O Zustand Store só recebe dados que já passaram por todas as validações e correções.
Manutenibilidade: Futuras regras de negócio podem ser adicionadas facilmente no mesmo bloco de código.
Performance: A manipulação do array é feita uma única vez, no momento do recebimento dos dados, sendo uma operação extremamente rápida e de baixo custo.

# Plano de Implementação: Migração e Refinamento de Dados Pós-Enriquecimento

Este documento detalha a estratégia de implementação para refinar as sugestões de óleos essenciais da IA, aplicando uma lógica de "migração" e filtragem de dados no frontend. O objetivo é garantir que apenas informações precisas, seguras e relevantes sejam armazenadas no estado da aplicação (Zustand store) e apresentadas ao usuário.

## 1. Visão Geral do Processo (Para um Desenvolvedor Júnior)

O processo para obter a lista final de óleos para cada propriedade terapêutica acontece em duas fases principais. Entender essa separação é fundamental para compreender por que o refinamento é crucial.

### Fase 1: `suggested-oils` (A Sugestão Criativa da IA)

-   **O que acontece?** O frontend envia a preocupação de saúde, demografia, causas e sintomas do usuário para a nossa IA. A IA, agindo como uma especialista em aromaterapia, retorna uma lista de óleos essenciais que ela acredita serem relevantes para cada propriedade terapêutica.
-   **O que recebemos?** Uma lista de óleos com `name_english`, `name_botanical` (sugerido pela IA), e um `relevancy_to_property_score`.
-   **O "Problema":** A IA é criativa, mas não perfeita. Ela pode "inventar" nomes botânicos, sugerir óleos que não existem no nosso banco de dados ou não conhecer as informações de segurança específicas que temos. Não podemos confiar 100% nesta lista inicial.

### Fase 2: `oil-enrichment` (A Validação e Enriquecimento do Nosso Sistema)

-   **O que acontece?** O frontend pega a lista de óleos sugerida pela IA e a envia para o nosso próprio backend (`/api/ai/batch-enrichment`). Nosso sistema então compara cada óleo sugerido com o nosso banco de dados de óleos (Supabase).
-   **O que fazemos?**
    1.  **Validação:** Verificamos se o óleo realmente existe.
    2.  **Similaridade:** Calculamos o quão parecido o óleo sugerido é com os óleos do nosso banco (`similarity_score`).
    3.  **Enriquecimento:** Se encontramos uma correspondência, adicionamos dados cruciais que a IA não fornece, como `safety` (informações de segurança), `constituents`, etc.
-   **O Resultado:** Uma lista de óleos "enriquecida", onde cada óleo agora tem um `enrichment_status` ('enriched', 'discarded', 'not_found'), um `similarity_score`, e dados de segurança validados.

É neste ponto, **após a Fase 2**, que temos todos os dados necessários para aplicar as estratégias de refinamento inteligentes descritas abaixo, antes de salvar a lista final na store.

---

## 2. O Ponto de Intervenção Estratégico

A implementação deve ser feita em um único local para seguir o princípio **DRY (Don't Repeat Yourself)**.

-   **Arquivo:** `src/features/create-recipe/components/properties-display.tsx`
-   **Local Exato:** Dentro da função `handleStreamingResults`, na `async` IIFE (Immediately Invoked Function Expression) que faz a chamada `fetch` para `/api/ai/batch-enrichment`.

**Por que aqui?** É o único ponto no sistema onde temos acesso simultâneo a:
a) A lista de óleos sugeridos pela IA (`suggested_oils`).
b) A lista enriquecida e validada pelo nosso backend (`enrichmentResult`).
c) Os dados demográficos do usuário (via `useRecipeStore.getState()`).

Ao processar os dados aqui, garantimos que qualquer dado que entre no Zustand Store já esteja 100% validado, corrigido e filtrado, simplificando todos os outros componentes que consomem esses dados.

```mermaid
sequenceDiagram
    participant Comp as properties-display.tsx
    participant Store as useRecipeStore
    participant API as /api/ai/batch-enrichment

    Note over Comp: Recebe `enrichmentResult` da API
    Comp->>Store: 1. `useRecipeStore.getState()` para buscar `demographics`
    Comp->>Comp: 2. **Aplica Pipeline de Refinamento** (map, filter, uniqBy)
    Note over Comp: Gera a lista `finalOils`
    Comp->>Store: 3. `updatePropertyWithEnrichedOils(p.property_id, finalOils)`
    Store-->>Comp: Estado atualizado com dados limpos e seguros
```

---

## 3. Implementação Unificada do Refinamento

A lógica de migração e refinamento deve ser implementada como um pipeline de operações de array, encadeadas para máxima clareza e eficiência.

```typescript
// Em: src/features/create-recipe/components/properties-display.tsx
// Dentro da função async que faz o fetch para /api/ai/batch-enrichment

import _ from 'lodash'; // Importar no topo do arquivo

// ...
const enrichmentResult = await response.json();
const { demographics } = useRecipeStore.getState(); // Pega a demografia da store

// --- INÍCIO DO PIPELINE DE MIGRAÇÃO E REFINAMENTO ---

const finalOils = _.uniqBy( // 4. Consolidação de Duplicatas
  enrichmentResult.enriched_oils
    .map(enrichedOil => { // 1. Migração de Qualidade e Scoring
      // Descarta óleos de baixa similaridade ou não encontrados
      if (enrichedOil.enrichment_status === 'discarded' || enrichedOil.enrichment_status === 'not_found') {
        return null;
      }

      // Óleo válido, prosseguir com o enriquecimento
      const confidence_score = (enrichedOil.relevancy_to_property_score / 5) * enrichedOil.similarity_score;

      return {
        ...enrichedOil,
        // Corrige o nome botânico se houver mismatch
        name_botanical: enrichedOil.botanical_mismatch ? enrichedOil.name_scientific : enrichedOil.name_botanical,
        isEnriched: true,
        confidence_score: parseFloat(confidence_score.toFixed(2)),
      };
    })
    .filter(Boolean) // Remove os nulos (óleos descartados)
    .filter(oil => { // 2. Filtragem por Demografia
      if (demographics.ageCategory === 'child' && (oil.safety?.dermocaustic || oil.safety?.neurotoxic)) {
          console.log(`🛡️ SAFETY FILTER: Removendo "${oil.name_english}" para criança.`);
          return false;
      }
      // Adicionar outras regras de segurança aqui (ex: gravidez, idosos)
      return true;
    }),
  'name_scientific' // Chave para desduplicação
) as EnrichedEssentialOil[];

// --- FIM DO PIPELINE ---

// Agora, atualizamos a store com a lista final, limpa e segura.
updatePropertyWithEnrichedOils(p.property_id, finalOils);
setPropertyEnrichmentStatus(p.property_id, 'success');
console.log(`✅ Batch enrichment SUCCESS for ${p.property_name} with ${finalOils.length} valid oils.`);
```

---

## 3. Resumo das Propostas

| Estratégia | Objetivo | Dados Utilizados | Momento da Aplicação |
| :--- | :--- | :--- | :--- |
| **1. Migração & Scoring** | Corrigir dados da IA e calcular `confidence_score` | `enrichment_result`, `relevancy_score` | `.map()` |
| **2. Filtro Demográfico** | Garantir segurança demográfica | `demographics.ageCategory`, `oil.safety` | `.filter()` |
| **3. Consolidação** | Remover duplicatas pós-correção | `name_scientific` validado | `_.uniqBy()` |
| **Rebaixamento (UI)** | Penalizar `botanical_mismatch` | `oil.botanical_mismatch` | Na ordenação da UI |

---

## 4. Benefícios da Abordagem Unificada

-   **Ponto Único de Verdade:** Toda a lógica de refinamento de dados reside em um só lugar.
-   **Integridade do Estado:** O Zustand Store só recebe dados que já passaram por todas as validações, correções e filtros.
-   **Manutenibilidade:** Futuras regras de negócio (ex: mais filtros de segurança) podem ser adicionadas facilmente no mesmo pipeline.
-   **Performance:** A manipulação do array é feita uma única vez, no momento do recebimento dos dados, sendo uma operação extremamente rápida e de baixo custo.

1. Implementação de Cache para Enriquecimento de Óleos
Como as buscas {name + botanical name} serão frequentes, um cache simples pode ser implementado no backend (em BatchEnrichmentService) para evitar chamadas repetidas ao OpenAI e ao Supabase para óleos já processados recentemente.

// Exemplo de cache in-memory (pode ser melhorado para Redis em produção)
const oilEnrichmentCache = new Map<string, EnrichedEssentialOil>();

function getCacheKey(nameEnglish: string, nameBotanical: string) {
  return `${nameEnglish.toLowerCase()}|${nameBotanical.toLowerCase()}`;
}

// Antes de processar cada óleo:
const cacheKey = getCacheKey(oil.name_english, oil.name_botanical);
if (oilEnrichmentCache.has(cacheKey)) {
  return oilEnrichmentCache.get(cacheKey);
}
// ...processa normalmente e salva no cache após enriquecer
oilEnrichmentCache.set(cacheKey, enrichedOil);

Benefício: Reduz custo e latência, principalmente para óleos populares.

2. Pipeline de Refinamento Centralizado
O pipeline sugerido no next-oil-enrichment-migration.md já está bem DRY e centralizado no frontend, dentro de properties-display.tsx. Certifique-se de que:

Toda lógica de filtragem, scoring e deduplicação está em um único local.
Novas regras (ex: gravidez, idosos) sejam adicionadas como funções reutilizáveis dentro desse pipeline.
O pipeline seja facilmente testável (pode extrair para um utilitário se crescer muito).
3. Flags e Scores
Implemente o cálculo de confidence_score e is_ultra_safe diretamente no pipeline, como já sugerido.
Para o score de relevância da receita (recipe_relevancy_score), garanta que o valor de property.relevancy_score esteja disponível no contexto do pipeline.
4. Deduplicação e Ordenação
Use sempre name_scientific para deduplicar.
Ordene os óleos por confidence_score (decrescente) e, em seguida, rebaixe os com botanical_mismatch para o final da lista.
5. Testes e Logs
Adicione logs claros para cada etapa do pipeline, facilitando o debug.
Implemente testes unitários para o pipeline de refinamento, garantindo que regras de segurança e deduplicação estejam corretas.
6. Sugestão Extra: Cache no Frontend (Opcional)
Se notar que o mesmo usuário faz buscas repetidas, pode-se adicionar um cache simples no frontend (ex: Zustand ou Context API) para evitar reprocessamento local.