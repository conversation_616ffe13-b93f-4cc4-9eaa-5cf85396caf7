/**
 * @fileoverview Properties Display component for Essential Oil Recipe Creator.
 * Displays therapeutic properties already loaded by Symptoms Selection step and handles oil suggestions.
 * OPTIMIZED: Removed duplicate properties streaming - properties are loaded in Symptoms Selection step.
 */

'use client';

import React, { useEffect, useCallback, useRef } from 'react';
import { useRecipeStore } from '../store/recipe-store';
import { useRecipeWizardNavigation } from '../hooks/use-recipe-navigation';
import { useCreateRecipeStreaming } from '../hooks/use-create-recipe-streaming';
import { useApiLanguage } from '@/lib/i18n/utils';
import { TherapeuticPropertiesTable } from './therapeutic-properties-table';
import { 
  TherapeuticProperty, 
  EnrichedEssentialOil,
  EssentialOil
} from '../types/recipe.types';

import { Button } from '@/components/ui/button';
import { RecipeNavigationButtons } from './recipe-navigation-buttons';

// Add the triggerBatchEnrichment function
async function triggerBatchEnrichment(propertyId: string, suggestedOils: EssentialOil[]) {
  const response = await fetch('/api/ai/batch-enrichment', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ suggestedOils })
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();
  if (!data.enriched_oils) {
    throw new Error('No enriched oils in response');
  }

  return data;
}

// Add a helper to log the full state of therapeuticProperties
function logTherapeuticPropertiesState(label: string, properties: TherapeuticProperty[]) {
  console.log(`🟦 [DEBUG] ${label} - therapeuticProperties:`);
  properties.forEach((p, idx) => {
    console.log(`  [${idx}] ${p.property_id} | ${p.property_name} | isEnriched: ${p.isEnriched} | oils: ${p.suggested_oils?.length || 0}`);
    if (p.suggested_oils) {
      p.suggested_oils.forEach((oil, oidx) => {
        console.log(`    [oil ${oidx}] ${oil.oil_id} | ${oil.name_english} | status: ${oil.enrichment_status}`);
      });
    }
  });
}

/**
 * Properties Display component - displays already-loaded therapeutic properties with oil suggestion capabilities
 */
export function PropertiesDisplay() {
  const {
    healthConcern,
    demographics,
    selectedCauses,
    selectedSymptoms,
    therapeuticProperties,
    updateTherapeuticProperties,
    error,
    setError,
    clearError,
    shouldAutoAnalyzeProperties,
    setShouldAutoAnalyzeProperties,
    propertyEnrichmentStatus,
    setPropertyEnrichmentStatus,
    updatePropertyWithEnrichedOils
  } = useRecipeStore();

  // Remove processedPropertiesRef initialization

  // Selectors for functions to prevent unnecessary re-renders
  // These are now directly available from the main hook above
  // const updatePropertyWithEnrichedOils = useRecipeStore(state => state.updatePropertyWithEnrichedOils);
  // const setPropertyEnrichmentStatus = useRecipeStore(state => state.setPropertyEnrichmentStatus);

  const { goToNext, goToPrevious, canGoNext, canGoPrevious, markCurrentStepCompleted } = useRecipeWizardNavigation();
  const userLanguage = useApiLanguage();

  // Get parallel streaming functionality
  const { 
    streamingState: parallelStreamingState, 
    startOilSuggestionStreaming,
    resetState: resetParallelStreams 
  } = useCreateRecipeStreaming();

  /**
   * Check if required data is available (properties should be loaded by Symptoms Selection step)
   */
  const checkRequiredData = useCallback(() => {
    if (!healthConcern || !demographics || selectedCauses.length === 0 || selectedSymptoms.length === 0) {
      return; // Let navigation handle redirects
    }
    
    if (therapeuticProperties.length === 0) {
      setError('Therapeutic properties not found. Please go back to the symptoms step.');
      return;
    }
    
    clearError();
    
    // Auto-mark step as completed when properties are available
    if (therapeuticProperties.length > 0) {
      markCurrentStepCompleted();
    }
  }, [healthConcern, demographics, selectedCauses, selectedSymptoms, therapeuticProperties.length, clearError, setError, markCurrentStepCompleted]);

  useEffect(() => {
    checkRequiredData();
  }, [checkRequiredData]);

  /**
   * Handle "Analyze All Properties" button click
   */
  const handleAnalyzeAllProperties = useCallback(async () => {
    if (!healthConcern || !demographics || therapeuticProperties.length === 0) {
      console.warn('Missing required data for oil suggestions');
      return;
    }

    console.log('ACTION: handleAnalyzeAllProperties');
    console.log('PAYLOAD:', { healthConcern, demographics, therapeuticProperties });
    console.log('🚀 Starting parallel oil analysis for all properties');
    
    // SIMPLIFIED: Only log essential info instead of full property details
    console.log('📤 SENDING - Properties for analysis:', therapeuticProperties.length, 'properties');

    try {
      // Reset previous state
      resetParallelStreams();
      
      // Start parallel streaming with all required parameters
      await startOilSuggestionStreaming(
        therapeuticProperties,
        healthConcern,
        demographics,
        selectedCauses,
        selectedSymptoms,
        userLanguage
      );

      console.log('📊 Parallel streaming started. Updating UI to loading state.');
      
      // Update UI to show loading state for all properties
      const loadingProperties = therapeuticProperties.map(p => ({
        ...p,
        isLoadingOils: true,
        errorLoadingOils: null,
      }));

      updateTherapeuticProperties(loadingProperties, 'parallelStreaming (Start)');
      logTherapeuticPropertiesState('After batch loading', loadingProperties);
      
    } catch (error) {
      console.error('❌ Failed to start parallel analysis:', error);
      setError('Failed to analyze properties. Please try again.');
    }
  }, [healthConcern, demographics, therapeuticProperties, resetParallelStreams, startOilSuggestionStreaming, selectedCauses, selectedSymptoms, userLanguage, updateTherapeuticProperties, setError]);

  // Ref to prevent double auto-trigger execution
  const autoTriggerExecutedRef = React.useRef(false);

  /**
   * Auto-trigger oil analysis when navigating to properties page
   * 
   * This effect implements the automatic oil analysis functionality that eliminates
   * the need for users to manually click "Analyze All Properties". The auto-trigger
   * only occurs when:
   * 
   * 1. shouldAutoAnalyzeProperties flag is true (set by symptoms component)
   * 2. Properties exist and some lack oil suggestions
   * 3. Required data (healthConcern, demographics) is available
   * 4. No streaming operation is currently active
   * 5. Auto-trigger hasn't already been executed (prevents double execution)
   * 
   * The flag is cleared immediately after triggering to prevent duplicate API calls.
   * A 800ms delay is added for better UX - allows users to see properties before analysis begins.
   * 
   * @see setShouldAutoAnalyzeProperties in symptoms-selection.tsx
   */
  useEffect(() => {
    // CRITICAL: Check execution guard FIRST, before any other logic
    if (autoTriggerExecutedRef.current) {
      console.log('❌ Auto-trigger skipped: already executed (early check)');
      return;
    }
    
    console.log('🔍 AUTO-TRIGGER DEBUG:', {
      shouldAutoAnalyzeProperties,
      autoTriggerExecuted: autoTriggerExecutedRef.current,
      propertiesCount: therapeuticProperties.length,
      hasHealthConcern: !!healthConcern,
      hasDemographics: !!demographics,
      isStreaming: parallelStreamingState.isStreaming
    });
    
    // Only proceed if auto-analysis is requested
    if (!shouldAutoAnalyzeProperties) {
      console.log('❌ Auto-trigger skipped: shouldAutoAnalyzeProperties is false');
      return;
    }
    
    const hasPropertiesWithoutOils = therapeuticProperties.some(p => !p.suggested_oils?.length);
    const hasRequiredData = healthConcern && demographics && therapeuticProperties.length > 0;
    const canAnalyze = hasPropertiesWithoutOils && hasRequiredData && !parallelStreamingState.isStreaming;
    
    console.log('🔍 AUTO-TRIGGER CONDITIONS:', {
      hasPropertiesWithoutOils,
      hasRequiredData,
      canAnalyze,
      propertiesWithoutOils: therapeuticProperties.filter(p => !p.suggested_oils?.length).length
    });
    
    if (canAnalyze) {
      console.log('🤖 Auto-triggering oil analysis based on navigation intent');
      
      // CRITICAL: Mark as executed IMMEDIATELY to prevent any race conditions
      autoTriggerExecutedRef.current = true;
      
      // Clear the flag to prevent re-triggering
      setShouldAutoAnalyzeProperties(false);
      
      // Execute immediately without timeout to avoid cleanup issues
      // The 800ms delay was causing the timeout to be cancelled by re-renders
      console.log('🚀 About to call handleAnalyzeAllProperties...');
      console.log('🔍 Function details:', {
        functionExists: typeof handleAnalyzeAllProperties === 'function',
        functionName: handleAnalyzeAllProperties.name,
        requiredDataCheck: {
          healthConcern: !!healthConcern,
          demographics: !!demographics,
          propertiesLength: therapeuticProperties.length
        }
      });
      
      (async () => {
        try {
          console.log('🚀 Calling handleAnalyzeAllProperties now...');
          const result = await handleAnalyzeAllProperties();
          console.log('✅ handleAnalyzeAllProperties returned:', result);
          console.log('✅ Auto-trigger completed successfully');
        } catch (error) {
          console.error('❌ Auto-trigger failed:', error);
          console.error('❌ Error details:', error);
          // Reset execution flag on error to allow retry
          autoTriggerExecutedRef.current = false;
        }
      })();
    } else {
      console.log('❌ Auto-trigger conditions not met');
    }
    
    // Return undefined for cases where no cleanup is needed
    return undefined;
  }, [therapeuticProperties, shouldAutoAnalyzeProperties, healthConcern, demographics, parallelStreamingState.isStreaming, setShouldAutoAnalyzeProperties]);

  // Reset auto-trigger ref only when we get a fresh shouldAutoAnalyzeProperties=true from a new navigation
  // We use a more specific condition to avoid resetting during re-renders
  const prevShouldAutoAnalyzeRef = React.useRef(shouldAutoAnalyzeProperties);
  useEffect(() => {
    // Only reset if shouldAutoAnalyzeProperties changed from false to true (new navigation)
    if (shouldAutoAnalyzeProperties && !prevShouldAutoAnalyzeRef.current) {
      console.log('🔄 Resetting auto-trigger ref for new navigation');
      autoTriggerExecutedRef.current = false;
    }
    prevShouldAutoAnalyzeRef.current = shouldAutoAnalyzeProperties;
  }, [shouldAutoAnalyzeProperties]);

  /**
   * Handle parallel streaming state updates - only when streaming starts
   */
  const isStreamingRef = useRef(parallelStreamingState.isStreaming);
  useEffect(() => {
    // Only trigger when streaming starts (false -> true transition)
    if (parallelStreamingState.isStreaming && !isStreamingRef.current) {
      console.log('📊 Parallel streaming started. Updating UI to loading state.');
      const currentProperties = useRecipeStore.getState().therapeuticProperties;
      updateTherapeuticProperties(
        currentProperties.map(p => ({
          ...p,
          isLoadingOils: !p.suggested_oils?.length,
          errorLoadingOils: null
        })),
        'parallelStreaming (Start)'
      );
    }
    isStreamingRef.current = parallelStreamingState.isStreaming;
  }, [parallelStreamingState.isStreaming]);

  // SOLUTION: Fix stale closure by using fresh state and proper dependencies
  const handleStreamingResults = useCallback(() => {
    if (parallelStreamingState.results.size === 0) return;

    console.log('ACTION: handleStreamingResults');
    console.log('PAYLOAD:', parallelStreamingState.results);

    // By including therapeuticProperties in the dependency array below,
    // this function always has the latest state and avoids overwriting
    // completed enrichments with stale data.
    therapeuticProperties.forEach(async (p: TherapeuticProperty) => {
      // Skip if already processed this result
      if (propertyEnrichmentStatus[p.property_id] === 'loading' || propertyEnrichmentStatus[p.property_id] === 'success') {
        return;
      }

      // Handle streaming results for each property
      if (parallelStreamingState.results.has(p.property_id)) {
        const result = parallelStreamingState.results.get(p.property_id);
        const suggested_oils = result?.suggested_oils.map((oil: EssentialOil) => ({
          ...oil,
          isEnriched: oil.isEnriched ?? false
        })) || [];

        // Only trigger enrichment if we have oils to enrich and property hasn't been enriched yet
        if (suggested_oils.length > 0 && !p.isEnriched) {
          console.log(`ACTION: triggerBatchEnrichment for property: ${p.property_id}`);
          console.log('PAYLOAD:', suggested_oils);
          console.log(`🔄 [properties-display] Triggering enrichment for property ${p.property_id} with ${suggested_oils.length} oils`);
          
          setPropertyEnrichmentStatus(p.property_id, 'loading');
          
          try {
            const data = await triggerBatchEnrichment(p.property_id, suggested_oils);
            
            await updatePropertyWithEnrichedOils(p.property_id, data.enriched_oils);
            logTherapeuticPropertiesState(`After enrichment for property ${p.property_id}`, useRecipeStore.getState().therapeuticProperties);
            
            const allOilsProcessed = data.enriched_oils.every((oil: EnrichedEssentialOil) => 
              oil.enrichment_status === 'enriched' || 
              oil.enrichment_status === 'not_found' || 
              oil.enrichment_status === 'discarded'
            );
            setPropertyEnrichmentStatus(p.property_id, allOilsProcessed ? 'success' : 'error');
            
            console.log(`✅ [properties-display] Enrichment completed for property ${p.property_id}. All oils processed: ${allOilsProcessed}`);
          } catch (error) {
            console.error(`❌ [properties-display] Enrichment failed for property ${p.property_id}:`, error);
            setPropertyEnrichmentStatus(p.property_id, 'error');
            logTherapeuticPropertiesState(`After error for property ${p.property_id}`, useRecipeStore.getState().therapeuticProperties);
          }
        }
      }
    });
  }, [
    parallelStreamingState.results, 
    therapeuticProperties, 
    propertyEnrichmentStatus, 
    updatePropertyWithEnrichedOils, 
    setPropertyEnrichmentStatus
  ]);

  // Remove processedPropertiesRef cleanup effect

  /**
   * Handle parallel streaming results
   */
  useEffect(() => {
    handleStreamingResults();
  }, [handleStreamingResults]);

  /**
   * Handle parallel streaming errors
   */
  useEffect(() => {
    if (parallelStreamingState.errors.size === 0) return;

    const currentProperties = useRecipeStore.getState().therapeuticProperties;

    parallelStreamingState.errors.forEach((errorMsg, propertyId) => {
      // Only update the error/loading state for the affected property
      const property = currentProperties.find(p => p.property_id === propertyId);
      if (property) {
        updatePropertyWithEnrichedOils(propertyId, property.suggested_oils || []);
        setPropertyEnrichmentStatus(propertyId, 'error');
        logTherapeuticPropertiesState(`After error for property ${propertyId}`, useRecipeStore.getState().therapeuticProperties);
      }
    });
  }, [parallelStreamingState.errors, updatePropertyWithEnrichedOils, setPropertyEnrichmentStatus]);

  /**
   * Monitor property-level progress (only meaningful progress we can track)
   * Each property agent decides dynamically how many tools to call, so tool progress is meaningless
   */
  const propertyProgressCount = React.useMemo(() => {
    if (!parallelStreamingState.isStreaming) return { completed: 0, total: 0 };
    
    // Count properties that have completed (either with results or errors)
    const completedCount = parallelStreamingState.results.size + parallelStreamingState.errors.size;
    const totalProperties = therapeuticProperties.length;
    
    return {
      completed: completedCount,
      total: totalProperties
    };
  }, [parallelStreamingState.isStreaming, parallelStreamingState.results.size, parallelStreamingState.errors.size, therapeuticProperties.length]);

  useEffect(() => {
    if (parallelStreamingState.isStreaming) {
      console.log(`📊 Property Analysis Progress: ${propertyProgressCount.completed}/${propertyProgressCount.total} properties completed`);
      console.log(`📊 Tool Calls Made: ${parallelStreamingState.completedCount} total | Results: ${parallelStreamingState.results.size} | Errors: ${parallelStreamingState.errors.size}`);
    }
  }, [parallelStreamingState.isStreaming, propertyProgressCount, parallelStreamingState.completedCount, parallelStreamingState.results.size, parallelStreamingState.errors.size]);

  /**
   * Navigation handlers
   */
  const handleContinue = async () => {
    try {
      markCurrentStepCompleted();
      if (canGoNext()) {
        await goToNext();
      }
    } catch (error) {
      console.error('Navigation failed:', error);
      setError('Failed to proceed to next step. Please try again.');
    }
  };

  const handleGoBack = async () => {
    if (canGoPrevious()) {
      await goToPrevious();
    }
  };

  // Error state
  if (error) {
    return (
      <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
        <div className="flex items-center justify-between">
          <p className="text-destructive text-sm">{error}</p>
          <Button variant="outline" size="sm" onClick={handleGoBack}>
            ← Go Back
          </Button>
        </div>
      </div>
    );
  }

  // Empty state - should not happen if navigation works correctly
  if (therapeuticProperties.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">
          No therapeutic properties found. Please go back to the symptoms step.
        </p>
        <Button 
          onClick={handleGoBack} 
          className="mt-4"
          variant="outline"
        >
          ← Go Back to Symptoms
        </Button>
      </div>
    );
  }

  // Main content
  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Therapeutic Properties</h2>
            <p className="text-muted-foreground">
              {parallelStreamingState.isStreaming ? (
                <>Analyzing properties to find the best essential oils... ({propertyProgressCount.completed}/{propertyProgressCount.total} completed)</>
              ) : (
                <>Essential oil recommendations for your therapeutic properties {therapeuticProperties.some(p => p.suggested_oils?.length) ? 'ready below' : 'loading...'}</>
              )}
            </p>
          </div>
          
          {/* Keep manual button for re-analysis */}
          {!parallelStreamingState.isStreaming && (
            <Button 
              onClick={handleAnalyzeAllProperties}
              variant="ghost"
              size="sm"
              className="text-xs"
            >
              Re-analyze
            </Button>
          )}
        </div>
      </div>

      {/* Properties Table */}
      <div className="rounded-md border">
        <TherapeuticPropertiesTable
          properties={therapeuticProperties}
          selectedCauses={selectedCauses}
          selectedSymptoms={selectedSymptoms}
          parallelStreamingState={parallelStreamingState}
          propertyEnrichmentStatus={propertyEnrichmentStatus}
          setPropertyEnrichmentStatus={setPropertyEnrichmentStatus}
          updatePropertyWithEnrichedOils={updatePropertyWithEnrichedOils}
        />
      </div>

      {/* Navigation Buttons */}
      <RecipeNavigationButtons
        onPrevious={handleGoBack}
        onNext={handleContinue}
        canGoPrevious={canGoPrevious()}
        canGoNext={canGoNext()}
        isValid={true}
        isLoading={false}
        previousLabel="Previous"
        nextLabel="Continue to Next Step"
        statusMessage="Analysis complete"
        statusType="success"
      />
    </div>
  );
}