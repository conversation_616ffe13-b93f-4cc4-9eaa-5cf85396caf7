/**
 * @fileoverview Symptoms Selection component for Essential Oil Recipe Creator.
 * Loads potential symptoms and handles therapeutic properties streaming upon submission.
 * OPTIMIZED: Uses centralized streaming utilities and proper data transformation patterns.
 */

'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Brain } from 'lucide-react';
import { useRecipeStore } from '../store/recipe-store';
import { useRecipeWizardNavigation } from '../hooks/use-recipe-navigation';
import type { PotentialSymptom } from '../types/recipe.types';
import { cn } from '@/lib/utils';
import { useAIStreaming } from '@/lib/ai/hooks/use-ai-streaming';
import AIStreamingModal from '@/components/ui/ai-streaming-modal';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { createStreamRequest } from '../utils/api-data-transform';
import { transformRecipeWizardData, extractFinalStreamingData } from '@/lib/ai/utils/streaming-utils';
import { useApiLanguage } from '@/lib/i18n/utils';
import { RecipeNavigationButtons } from './recipe-navigation-buttons';

/**
 * Symptoms Selection component
 */
export function SymptomsSelection() {
  const apiLanguage = useApiLanguage();

  const {
    healthConcern,
    demographics,
    selectedCauses,
    selectedSymptoms,
    potentialSymptoms,
    updateSelectedSymptoms,
    setPotentialSymptoms,
    therapeuticProperties,
    updateTherapeuticProperties,
    error,
    setError,
    clearError,
    isStreamingProperties,
    setStreamingProperties,
    setShouldAutoAnalyzeProperties
  } = useRecipeStore();

  const { goToNext, goToPrevious, canGoNext, canGoPrevious, markCurrentStepCompleted } = useRecipeWizardNavigation();
  const [selectedSymptomIds, setSelectedSymptomIds] = useState<Set<string>>(new Set());
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [streamingItems, setStreamingItems] = useState<any[]>([]);

  // Navigation refs
  const hasNavigatedRef = useRef(false);
  const hasAutoLoadedRef = useRef(false);

  // AI Streaming for symptoms
  const { 
    startStream, 
    partialData, 
    isStreaming, 
    isComplete, 
    finalData, 
    error: symptomsStreamingError 
  } = useAIStreaming({
    jsonArrayPath: 'data.potential_symptoms'
  });

  // AI Streaming for therapeutic properties
  const {
    startStream: startPropertiesStream,
    partialData: propertiesPartialData,
    isStreaming: isStreamingPropertiesData,
    isComplete: isPropertiesComplete,
    finalData: propertiesFinalData,
    error: propertiesStreamingError
  } = useAIStreaming({
    jsonArrayPath: 'data.therapeutic_properties',
    timeout: 60000,
    maxRetries: 2
  });

  /**
   * Initialize selected symptoms from store
   */
  useEffect(() => {
    if (selectedSymptoms.length > 0) {
      const ids = new Set(selectedSymptoms.map(symptom => symptom.symptom_id));
      setSelectedSymptomIds(ids);
    }
  }, [selectedSymptoms]);

  /**
   * OPTIMIZED: Handle symptoms streaming using centralized utilities
   */
  useEffect(() => {
    if (partialData && Array.isArray(partialData) && partialData.length > 0) {
      const transformedSymptoms = transformRecipeWizardData(partialData, 'symptoms');
      if (transformedSymptoms.length > 0) {
        setPotentialSymptoms(transformedSymptoms);

        const modalItems = partialData.map((symptom: any) => ({
          title: symptom.name_localized,
          subtitle: symptom.suggestion_localized,
          description: symptom.explanation_localized,
          timestamp: new Date()
        }));
        setStreamingItems(modalItems);
      }
    }
  }, [partialData, setPotentialSymptoms]);

  /**
   * OPTIMIZED: Handle symptoms completion using centralized utilities
   */
  useEffect(() => {
    if (isComplete && finalData) {
      setIsModalOpen(false);
      hasAutoLoadedRef.current = false;

      const finalSymptoms = extractFinalStreamingData(finalData, 'data.potential_symptoms');
      const transformedSymptoms = transformRecipeWizardData(finalSymptoms, 'symptoms');

      if (transformedSymptoms.length > 0) {
        setPotentialSymptoms(transformedSymptoms);
      }
    }
  }, [isComplete, finalData, setPotentialSymptoms]);

  /**
   * OPTIMIZED: Handle therapeutic properties partial updates using centralized utilities
   */
  useEffect(() => {
    if (propertiesPartialData && Array.isArray(propertiesPartialData) && propertiesPartialData.length > 0) {
      const transformedProperties = transformRecipeWizardData(propertiesPartialData, 'properties');
      if (transformedProperties.length > 0) {
        updateTherapeuticProperties(transformedProperties, 'SymptomsSelection (Streaming)');
      }
    }
  }, [propertiesPartialData, updateTherapeuticProperties]);

  /**
   * OPTIMIZED: Handle therapeutic properties completion using centralized utilities
   */
  useEffect(() => {
    if (isPropertiesComplete && propertiesFinalData && !hasNavigatedRef.current) {
      hasNavigatedRef.current = true;

      const finalProperties = extractFinalStreamingData(propertiesFinalData, 'data.therapeutic_properties');
      const transformedProperties = transformRecipeWizardData(finalProperties, 'properties');

      if (transformedProperties.length > 0) {
        updateTherapeuticProperties(transformedProperties, 'SymptomsSelection (Final)');
      }

      setStreamingProperties(false);
      if (canGoNext()) {
        goToNext();
      }
    }
  }, [isPropertiesComplete, propertiesFinalData, canGoNext, goToNext, setStreamingProperties, updateTherapeuticProperties]);

  /**
   * Handle streaming errors
   */
  useEffect(() => {
    if (symptomsStreamingError) {
      setError(`Failed to load potential symptoms: ${symptomsStreamingError}`);
      setIsModalOpen(false);
      hasAutoLoadedRef.current = false;
    }
  }, [symptomsStreamingError, setError]);

  useEffect(() => {
    if (propertiesStreamingError) {
      setError(`Failed to analyze therapeutic properties: ${propertiesStreamingError}`);
      setStreamingProperties(false);
      hasNavigatedRef.current = false;
    }
  }, [propertiesStreamingError, setError, setStreamingProperties]);

  /**
   * Sync streaming state with store
   */
  useEffect(() => {
    setStreamingProperties(isStreamingPropertiesData);
  }, [isStreamingPropertiesData, setStreamingProperties]);

  /**
   * OPTIMIZED: Load potential symptoms using centralized request building
   */
  const loadPotentialSymptoms = useCallback(async () => {
    if (!healthConcern || !demographics || selectedCauses.length === 0) {
      return;
    }

    if (potentialSymptoms.length > 0 || isStreaming) {
      return;
    }

    clearError();
    setIsModalOpen(true);

    try {
      const requestData = createStreamRequest(
        'create-recipe',
        'potential-symptoms',
        healthConcern,
        demographics,
        selectedCauses,
        [],
        apiLanguage
      );

      await startStream('/api/ai/streaming', requestData);
    } catch (error) {
      setError('Failed to load potential symptoms. Please try again.');
      setIsModalOpen(false);
      hasAutoLoadedRef.current = false;
    }
  }, [healthConcern, demographics, selectedCauses, potentialSymptoms.length, startStream, setError, clearError, apiLanguage, isStreaming]);

  /**
   * Check required data availability
   */
  useEffect(() => {
    if (!healthConcern || !demographics || selectedCauses.length === 0) {
      return;
    }

    if (potentialSymptoms.length === 0 && !isStreaming) {
      setError('Potential symptoms not found. Please go back to the causes step to generate them.');
      return;
    }

    clearError();
  }, [healthConcern, demographics, selectedCauses.length, potentialSymptoms.length, isStreaming, setError, clearError]);

  /**
   * Handle symptom selection toggle
   */
  const handleSymptomToggle = useCallback((symptom: PotentialSymptom, e?: React.MouseEvent) => {
    e?.stopPropagation();
    
    const symptomId = symptom.symptom_id;

    if (!symptomId) {
      setError('Invalid symptom data. Please refresh and try again.');
      return;
    }

    setSelectedSymptomIds(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(symptomId)) {
        newSelected.delete(symptomId);
      } else {
        newSelected.add(symptomId);
        clearError();
      }
      return newSelected;
    });

    // Update store with selected symptoms
    setTimeout(() => {
      const newSelectedIds = new Set(selectedSymptomIds);
      if (newSelectedIds.has(symptomId)) {
        newSelectedIds.delete(symptomId);
      } else {
        newSelectedIds.add(symptomId);
      }
      
      const newSelectedSymptoms = potentialSymptoms.filter(s => 
        newSelectedIds.has(s.symptom_id)
      );
      
      updateSelectedSymptoms(newSelectedSymptoms);
      
      if (newSelectedSymptoms.length > 0) {
        markCurrentStepCompleted();
      }
    }, 0);
  }, [potentialSymptoms, selectedSymptomIds, updateSelectedSymptoms, markCurrentStepCompleted, setError, clearError]);

  /**
   * OPTIMIZED: Handle form submission using centralized request building
   */
  const onSubmit = async () => {
    if (selectedSymptomIds.size === 0) {
      setError('Please select at least one symptom.');
      return;
    }

    try {
      markCurrentStepCompleted();
      clearError();
      hasNavigatedRef.current = false;
      setStreamingProperties(true);

      // Set auto-analysis flag for properties page
      // This enables automatic oil analysis when user reaches properties page
      setShouldAutoAnalyzeProperties(true);

      const requestData = createStreamRequest(
        'create-recipe',
        'therapeutic-properties',
        healthConcern!,
        demographics!,
        selectedCauses,
        selectedSymptoms,
        apiLanguage
      );

      await startPropertiesStream('/api/ai/streaming', requestData);

    } catch (error) {
      setError('Failed to analyze therapeutic properties. Please try again.');
      setStreamingProperties(false);
      hasNavigatedRef.current = false;
      // Clear the flag on error to prevent auto-trigger in error state
      setShouldAutoAnalyzeProperties(false);
    }
  };

  /**
   * Navigation handlers
   */
  const handleGoBack = async () => {
    if (canGoPrevious()) {
      await goToPrevious();
    }
  };

  const handleRetry = () => {
    clearError();
    hasAutoLoadedRef.current = false;
    loadPotentialSymptoms();
  };

  const isFormValid = selectedSymptomIds.size > 0 && selectedSymptomIds.size <= potentialSymptoms.length;

  return (
    <div data-testid="symptoms-selection" className="space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <h2 className="text-2xl font-bold text-foreground">
          What symptoms are you experiencing?
        </h2>
        <p className="text-muted-foreground">
          Based on your selected causes, here are potential symptoms. Select all that you're currently experiencing.
        </p>
      </div>

      {/* Selected Causes Summary */}
      {selectedCauses.length > 0 && (
        <div className="bg-muted/50 rounded-lg p-4">
          <h3 className="text-sm font-medium text-foreground mb-2">Selected causes:</h3>
          <div className="flex flex-wrap gap-2">
            {selectedCauses.map((cause, index) => (
              <span
                key={`${cause.cause_name}-${index}`}
                className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-md"
              >
                {cause.cause_name}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <p className="text-destructive text-sm">{error}</p>
            {error.includes('Failed to load') && (
              <button
                onClick={handleRetry}
                className="px-3 py-1 bg-destructive text-destructive-foreground rounded hover:bg-destructive/90 text-sm"
              >
                Retry
              </button>
            )}
          </div>
        </div>
      )}

      {/* Missing Data State */}
      {potentialSymptoms.length === 0 && !isStreaming && !error && (
        <div className="text-center py-12 space-y-6">
          <div className="space-y-2">
            <Brain className="h-12 w-12 text-muted-foreground mx-auto" />
            <h3 className="text-lg font-semibold text-muted-foreground">Missing Required Data</h3>
            <p className="text-muted-foreground max-w-md mx-auto">
              Potential symptoms not found. Please go back to the causes step to generate them.
            </p>
          </div>

          <button
            onClick={handleGoBack}
            className="mt-4 px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90"
          >
            ← Go Back to Causes
          </button>
        </div>
      )}

      {/* Symptoms Selection */}
      {potentialSymptoms.length > 0 && !isStreaming && (
        <div className="space-y-6">
          {/* Selection Counter */}
          <div className="flex justify-between items-center">
            <p className="text-sm text-muted-foreground">
              Select 1-{potentialSymptoms.length} symptoms that you're experiencing
            </p>
            <span className={cn(
              "text-sm font-medium",
              selectedSymptomIds.size > potentialSymptoms.length ? "text-destructive" : "text-foreground"
            )}>
              {selectedSymptomIds.size}/{potentialSymptoms.length} selected
            </span>
          </div>

          {/* Symptoms Table */}
          <div className="rounded-lg border border-border/50 bg-card/50 backdrop-blur-sm overflow-hidden shadow-sm">
            <Table>
              <TableHeader>
                <TableRow className="border-b border-border/50 bg-muted/30">
                  <TableHead className="w-16 h-14"></TableHead>
                  <TableHead className="font-semibold text-foreground">Symptoms</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {potentialSymptoms.map((symptom) => {
                  const isSelected = selectedSymptomIds.has(symptom.symptom_id);
                  
                  return (
                    <TableRow 
                      key={symptom.symptom_id}
                      className={cn(
                        'group relative cursor-pointer transition-all duration-200 border-b border-border/30 last:border-b-0',
                        'hover:bg-accent/50 hover:shadow-sm',
                        isSelected ? 'bg-primary/5 border-primary/20' : 'hover:border-border/60'
                      )}
                      onClick={(e) => handleSymptomToggle(symptom, e)}
                    >
                      <TableCell className="w-16 py-4">
                        <div 
                          className="flex items-center justify-center"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <div className={cn(
                            'flex items-center justify-center h-5 w-5 rounded-md border-2 transition-all duration-200',
                            'shadow-sm hover:shadow-md',
                            isSelected 
                              ? 'bg-primary border-primary shadow-primary/20' 
                              : 'border-input bg-background group-hover:border-primary/60 group-hover:bg-primary/5'
                          )}>
                            {isSelected && (
                              <svg 
                                className="h-3.5 w-3.5 text-primary-foreground" 
                                viewBox="0 0 14 14" 
                                fill="none"
                              >
                                <path 
                                  d="M11.6668 3.5L5.25016 9.91667L2.3335 7" 
                                  stroke="currentColor" 
                                  strokeWidth="2" 
                                  strokeLinecap="round" 
                                  strokeLinejoin="round"
                                />
                              </svg>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-4 pr-6">
                        <div className="space-y-2">
                          <div className="font-medium text-foreground group-hover:text-primary transition-colors duration-200">
                            {symptom.symptom_name}
                          </div>
                          {symptom.symptom_suggestion && (
                            <div className="text-sm text-muted-foreground leading-relaxed">
                              {symptom.symptom_suggestion}
                            </div>
                          )}
                          {symptom.explanation && (
                            <div className="text-sm text-muted-foreground/80 leading-relaxed italic">
                              {symptom.explanation}
                            </div>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          {/* Action Buttons */}
          <RecipeNavigationButtons
            onPrevious={handleGoBack}
            onNext={onSubmit}
            canGoPrevious={canGoPrevious()}
            canGoNext={canGoNext()}
            isValid={isFormValid}
            isLoading={false}
            previousLabel="Previous"
            nextLabel="Continue"
            statusMessage="Ready to continue"
            statusType="success"
          />
        </div>
      )}

      {/* AI Streaming Modal for Symptoms */}
      <AIStreamingModal
        isOpen={isModalOpen}
        title="AI Analysis in Progress"
        description="Identifying potential symptoms based on your selected causes"
        items={streamingItems}
        onClose={() => setIsModalOpen(false)}
        maxVisibleItems={100}
        analysisType="symptoms"
      />

      {/* AI Streaming Modal for Therapeutic Properties */}
      <AIStreamingModal
        isOpen={isStreamingProperties}
        title="AI Analysis in Progress"
        description="Identifying therapeutic properties to address your symptoms"
        items={therapeuticProperties.map((property, index) => ({
          id: `property-${index}-${property.property_name?.slice(0, 10) || 'unknown'}`,
          title: property.property_name || property.property_name_localized || `Therapeutic Property ${index + 1}`,
          subtitle: property.property_name_english || 'Therapeutic property',
          description: property.description || property.description_localized || '',
          timestamp: new Date()
        }))}
        onClose={() => console.log('User requested to close properties modal')}
        maxVisibleItems={100}
        analysisType="properties"
      />
    </div>
  );
} 