/**
 * Global i18n hook for the entire application
 * Integrates with existing language utilities
 */

import React, { useMemo, useEffect, useState } from 'react';
import { useUserLanguage } from '@/lib/i18n/utils';
import { mapLanguageCode, loadTranslations, type SupportedLocale, type TranslationMessages } from '@/lib/i18n';

interface TProps {
  k: string;
  fallback?: string;
  variables?: Record<string, string | number>;
}

/**
 * Global hook for internationalization across the entire app
 */
export function useI18n() {
  const userLanguage = useUserLanguage(); // Your existing hook
  const locale = useMemo(() => mapLanguageCode(userLanguage), [userLanguage]);
  
  const [translations, setTranslations] = useState<TranslationMessages>({});
  const [isLoading, setIsLoading] = useState(true);

  // Load translations when locale changes
  useEffect(() => {
    let mounted = true;
    
    const loadMessages = async () => {
      setIsLoading(true);
      try {
        const messages = await loadTranslations(locale);
        if (mounted) {
          setTranslations(messages);
        }
      } catch (error) {
        console.warn('Failed to load translations:', error);
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    };

    loadMessages();
    
    return () => {
      mounted = false;
    };
  }, [locale]);

  /**
   * Get nested translation value using dot notation
   */
  const getNestedValue = (obj: any, path: string): string | undefined => {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  };

  /**
   * Translation function
   * @param key - Translation key (supports dot notation)
   * @param fallback - Fallback text if translation not found
   * @param variables - Variables to interpolate
   */
  const t = (
    key: string, 
    fallback?: string, 
    variables?: Record<string, string | number>
  ): string => {
    let text = getNestedValue(translations, key) || fallback || key;

    // Simple variable interpolation
    if (variables && typeof text === 'string') {
      Object.entries(variables).forEach(([varKey, value]) => {
        text = text.replace(new RegExp(`\\{${varKey}\\}`, 'g'), String(value));
      });
    }

    return text;
  };

  return {
    t,
    locale,
    isLoading,
    // Utility functions
    isEnglish: locale === 'en',
    isPortuguese: locale === 'pt', 
    isSpanish: locale === 'es'
  };
}

/**
 * Simple component wrapper for translations
 * Usage: <T k="common.buttons.save" fallback="Save" />
 */
export function T({ k, fallback, variables }: TProps) {
  const { t } = useI18n();
  return React.createElement(React.Fragment, null, t(k, fallback, variables));
} 